style = dict(style_name    = 'blueskies',
             base_mpl_style='fast', 
             marketcolors  = {'candle'  : {'up':'w', 'down':'#0095ff'},
                              'edge'    : {'up':'k', 'down':'#0095ff'},
                              'wick'    : {'up':'k', 'down':'#0095ff'},
                              'ohlc'    : {'up':'#0095ff', 'down':'#0095ff'},
                              'volume'  : {'up':'w', 'down':'#0095ff'},
                              'vcdopcod': False,
                              'alpha'   : 1.0,
                             },
             mavcolors     = None,
             y_on_right    = False,
             facecolor     = '#dbf1ff',
             gridcolor     = None,
             gridstyle     = None,
             rc            = [('patch.linewidth'      ,  1.0      ),
                              ('patch.force_edgecolor', True      ),
                              ('lines.linewidth'      ,  1.0      ),
                              ('figure.titlesize'     , 'x-large' ),
                              ('figure.titleweight'   , 'semibold'),
                             ],
             base_mpf_style='blueskies', 
            )
