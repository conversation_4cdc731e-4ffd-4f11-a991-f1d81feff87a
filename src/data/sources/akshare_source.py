"""
akshare数据源实现
"""
import akshare as ak
import pandas as pd
from typing import List, Dict, Optional, Tuple
from datetime import datetime, timedelta
import time
import logging
import random
from functools import lru_cache

from ...core.interfaces.data_source import IDataSource
from ...core.exceptions.custom_exceptions import DataSourceError


class AkshareDataSource(IDataSource):
    """akshare数据源实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.retry_times = 3
        self.retry_delay = 1  # 秒
        self.request_delay_range = (0.3, 0.8)  # 随机延时范围，避免规律性请求
        self.batch_size = 50  # 批量处理大小
        self._cached_stock_list = None  # 股票列表缓存
        self._cached_trading_calendar = {}  # 交易日历缓存

    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "akshare"

    def get_stock_list(self, use_cache: bool = True) -> List[Dict]:
        """获取股票列表"""
        try:
            # 使用缓存
            if use_cache and self._cached_stock_list is not None:
                self.logger.info(f"使用缓存的股票列表，共 {len(self._cached_stock_list)} 只股票")
                return self._cached_stock_list

            self.logger.info("开始获取股票列表...")

            # 重试机制获取股票列表
            stock_info = self._retry_request(
                lambda: ak.stock_info_a_code_name(),
                "获取股票列表"
            )

            if stock_info is None or stock_info.empty:
                raise DataSourceError("获取股票列表失败：返回数据为空")

            result = []
            for _, row in stock_info.iterrows():
                stock_data = {
                    'stock_code': row['code'],
                    'stock_name': row['name'],
                    'industry': '',  # akshare基础接口不包含行业信息
                    'market': self._determine_market(row['code']),
                    'list_date': datetime.now()  # 暂时使用当前时间，后续可优化
                }
                result.append(stock_data)

            # 缓存结果
            self._cached_stock_list = result
            self.logger.info(f"成功获取股票列表，共 {len(result)} 只股票")
            return result

        except Exception as e:
            error_msg = f"获取股票列表失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataSourceError(error_msg)

    def get_daily_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """获取股票日K线数据"""
        try:
            self.logger.info(f"获取股票 {stock_code} 从 {start_date.date()} 到 {end_date.date()} 的数据")

            # 格式化日期
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            # 使用重试机制获取数据
            df = self._retry_request(
                lambda: ak.stock_zh_a_hist(
                    symbol=stock_code,
                    period="daily",
                    start_date=start_str,
                    end_date=end_str,
                    adjust=""
                ),
                f"获取股票 {stock_code} 日K线数据"
            )

            if df is None or df.empty:
                self.logger.warning(f"股票 {stock_code} 在指定时间段内无数据")
                return []

            # 转换数据格式
            result = self._convert_daily_data(df, stock_code)
            self.logger.info(f"成功获取股票 {stock_code} 数据，共 {len(result)} 条记录")
            return result

        except Exception as e:
            error_msg = f"获取股票 {stock_code} 日K线数据失败: {str(e)}"
            self.logger.error(error_msg)
            raise DataSourceError(error_msg)

    def get_realtime_data(self, stock_codes: List[str]) -> List[Dict]:
        """
        获取实时数据 - 已弃用
        注意：本系统只需要T-1交易日数据，不需要实时数据
        """
        self.logger.warning("get_realtime_data方法已弃用，系统只需要T-1交易日数据")
        return []

    def validate_stock_code(self, stock_code: str) -> bool:
        """验证股票代码是否有效"""
        try:
            # 简单的格式验证
            if not stock_code or len(stock_code) != 6:
                return False

            # 检查是否为数字
            if not stock_code.isdigit():
                return False

            # 检查是否为A股代码范围
            code_int = int(stock_code)
            if (code_int >= 600000 and code_int <= 699999) or \
               (code_int >= 1 and code_int <= 399999):
                return True

            return False

        except Exception:
            return False

    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取交易日历（排除节假日和周末）"""
        try:
            self.logger.info(f"获取交易日历：{start_date.date()} 到 {end_date.date()}")

            # 检查缓存
            cache_key = f"{start_date.year}"
            if cache_key in self._cached_trading_calendar:
                self.logger.info("使用缓存的交易日历")
                all_trade_dates = self._cached_trading_calendar[cache_key]
                # 筛选日期范围
                filtered_dates = [
                    date for date in all_trade_dates
                    if start_date <= date <= end_date
                ]
                self.logger.info(f"从缓存获取交易日历，共 {len(filtered_dates)} 个交易日")
                return filtered_dates

            # 使用重试机制获取交易日历
            trade_cal = self._retry_request(
                lambda: ak.tool_trade_date_hist_sina(),
                "获取交易日历"
            )

            if trade_cal is None or trade_cal.empty:
                # 如果获取失败，使用简单的工作日计算（排除周末）
                self.logger.warning("无法获取交易日历，使用简单工作日计算")
                return self._get_workdays(start_date, end_date)

            # 转换为datetime格式
            all_trade_dates = pd.to_datetime(trade_cal['trade_date']).tolist()

            # 缓存当年的交易日历
            self._cached_trading_calendar[cache_key] = all_trade_dates

            # 筛选日期范围
            filtered_dates = [
                date for date in all_trade_dates
                if start_date <= date <= end_date
            ]

            self.logger.info(f"成功获取交易日历，共 {len(filtered_dates)} 个交易日")
            return filtered_dates

        except Exception as e:
            self.logger.error(f"获取交易日历异常: {str(e)}")
            return self._get_workdays(start_date, end_date)

    def _get_workdays(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取工作日（简单版本，排除周末）"""
        workdays = []
        current_date = start_date

        while current_date <= end_date:
            # 排除周末（周六=5，周日=6）
            if current_date.weekday() < 5:
                workdays.append(current_date)
            current_date += timedelta(days=1)

        return workdays

    def _determine_market(self, stock_code: str) -> str:
        """根据股票代码判断市场"""
        if not stock_code or len(stock_code) != 6:
            return "未知"

        code_int = int(stock_code)

        if code_int >= 600000 and code_int <= 699999:
            return "上海主板"
        elif code_int >= 1 and code_int <= 299999:
            return "深圳主板"
        elif code_int >= 300000 and code_int <= 399999:
            return "创业板"
        else:
            return "其他"

    def _retry_request(self, request_func, operation_name: str):
        """
        通用重试请求方法

        Args:
            request_func: 请求函数
            operation_name: 操作名称，用于日志

        Returns:
            请求结果
        """
        for attempt in range(self.retry_times):
            try:
                # 添加随机延时，避免规律性请求
                if attempt > 0:
                    delay = random.uniform(*self.request_delay_range)
                    time.sleep(delay)

                result = request_func()
                return result

            except Exception as e:
                if attempt < self.retry_times - 1:
                    self.logger.warning(f"{operation_name}失败，第 {attempt + 1} 次重试: {str(e)}")
                    time.sleep(self.retry_delay * (attempt + 1))  # 递增延时
                else:
                    self.logger.error(f"{operation_name}最终失败: {str(e)}")
                    raise e

    def _convert_daily_data(self, df: pd.DataFrame, stock_code: str) -> List[Dict]:
        """
        转换日K线数据格式

        Args:
            df: akshare返回的DataFrame
            stock_code: 股票代码

        Returns:
            转换后的数据列表
        """
        result = []
        for _, row in df.iterrows():
            try:
                daily_data = {
                    'stock_code': stock_code,
                    'trade_date': pd.to_datetime(row['日期']),
                    'open_price': float(row['开盘']),
                    'close_price': float(row['收盘']),
                    'high_price': float(row['最高']),
                    'low_price': float(row['最低']),
                    'volume': int(row['成交量']),
                    'amount': float(row['成交额']),
                    'turnover_rate': float(row.get('换手率', 0)) if '换手率' in row else None
                }
                result.append(daily_data)
            except Exception as e:
                self.logger.warning(f"转换股票 {stock_code} 数据行失败: {str(e)}")
                continue

        return result

    def get_batch_daily_data(self, stock_codes: List[str],
                           start_date: datetime,
                           end_date: datetime) -> Dict[str, List[Dict]]:
        """
        批量获取多只股票的日K线数据

        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            Dict[str, List[Dict]]: 股票代码为key，数据列表为value的字典
        """
        result = {}
        total_count = len(stock_codes)

        self.logger.info(f"开始批量获取 {total_count} 只股票的数据")

        for i, stock_code in enumerate(stock_codes, 1):
            try:
                self.logger.info(f"获取股票 {stock_code} 数据 ({i}/{total_count})")

                daily_data = self.get_daily_data(stock_code, start_date, end_date)
                result[stock_code] = daily_data

                # 添加随机延时，避免请求过于频繁
                if i < total_count:  # 最后一个不需要延时
                    delay = random.uniform(*self.request_delay_range)
                    time.sleep(delay)

            except Exception as e:
                self.logger.error(f"获取股票 {stock_code} 数据失败: {str(e)}")
                result[stock_code] = []
                continue

        success_count = sum(1 for data in result.values() if data)
        self.logger.info(f"批量获取完成，成功: {success_count}/{total_count}")

        return result

    def clear_cache(self):
        """清除缓存"""
        self._cached_stock_list = None
        self._cached_trading_calendar.clear()
        self.logger.info("缓存已清除")
