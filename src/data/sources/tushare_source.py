"""
Tushare数据源实现
"""
import tushare as ts
import pandas as pd
from typing import List, Dict, Optional
from datetime import datetime, timedelta
import time
import logging
import random
from functools import lru_cache

from ...core.interfaces.data_source import IDataSource
from ...core.exceptions.custom_exceptions import DataSourceError
from ...config.tushare_config import tushare_config


class TushareDataSource(IDataSource):
    """Tushare数据源实现"""

    def __init__(self):
        self.logger = logging.getLogger(__name__)
        self.config = tushare_config

        # 初始化tushare
        if not self.config.is_token_configured():
            raise DataSourceError("Tushare token未配置，请设置环境变量TUSHARE_TOKEN")

        ts.set_token(self.config.get_token())
        self.pro = ts.pro_api()

        # 缓存
        self._cached_stock_list = None
        self._cached_trading_calendar = {}

        self.logger.info("Tushare数据源初始化成功")

    def get_data_source_name(self) -> str:
        """获取数据源名称"""
        return "tushare"

    def get_stock_list(self, use_cache: bool = True) -> List[Dict]:
        """获取股票列表"""
        try:
            # 使用缓存
            if use_cache and self._cached_stock_list is not None:
                self.logger.info(f"使用缓存的股票列表，共 {len(self._cached_stock_list)} 只股票")
                return self._cached_stock_list

            self.logger.info("开始获取股票列表...")

            # 获取股票基本信息
            stock_basic = self._retry_request(
                lambda: self.pro.stock_basic(
                    exchange='',
                    list_status='L',  # 上市状态
                    fields='ts_code,symbol,name,area,industry,market,list_date'
                ),
                "获取股票列表"
            )

            if stock_basic is None or stock_basic.empty:
                raise DataSourceError("获取股票列表失败：返回数据为空")

            result = []
            for _, row in stock_basic.iterrows():
                # 转换tushare代码格式到标准格式
                stock_code = self._convert_ts_code_to_standard(row['ts_code'])

                stock_data = {
                    'stock_code': stock_code,
                    'stock_name': row['name'],
                    'industry': row['industry'] if pd.notna(row['industry']) else '',
                    'market': self._determine_market_from_ts_code(row['ts_code']),
                    'list_date': self._parse_date(row['list_date']) if pd.notna(row['list_date']) else datetime.now()
                }
                result.append(stock_data)

            # 缓存结果
            self._cached_stock_list = result
            self.logger.info(f"成功获取股票列表，共 {len(result)} 只股票")
            return result

        except Exception as e:
            self.logger.error(f"获取股票列表失败: {str(e)}")
            raise DataSourceError(f"获取股票列表失败: {str(e)}")

    def get_daily_data(self, stock_code: str,
                      start_date: datetime,
                      end_date: datetime) -> List[Dict]:
        """获取股票日K线数据"""
        try:
            self.logger.info(f"获取股票 {stock_code} 从 {start_date.date()} 到 {end_date.date()} 的数据")

            # 转换股票代码格式
            ts_code = self._convert_standard_to_ts_code(stock_code)

            # 格式化日期
            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            # 使用重试机制获取数据
            df = self._retry_request(
                lambda: self.pro.daily(
                    ts_code=ts_code,
                    start_date=start_str,
                    end_date=end_str,
                    fields='ts_code,trade_date,open,high,low,close,vol,amount'
                ),
                f"获取股票 {stock_code} 日K线数据"
            )

            if df is None or df.empty:
                self.logger.warning(f"股票 {stock_code} 在指定时间段内无数据")
                return []

            # 转换数据格式
            result = self._convert_daily_data(df, stock_code)
            self.logger.info(f"成功获取股票 {stock_code} 数据，共 {len(result)} 条记录")
            return result

        except Exception as e:
            self.logger.error(f"获取股票 {stock_code} 日K线数据失败: {str(e)}")
            raise DataSourceError(f"获取股票 {stock_code} 日K线数据失败: {str(e)}")

    def get_realtime_data(self, stock_codes: List[str]) -> List[Dict]:
        """获取实时数据"""
        try:
            self.logger.info(f"获取 {len(stock_codes)} 只股票的实时数据")

            # 转换股票代码格式
            ts_codes = [self._convert_standard_to_ts_code(code) for code in stock_codes]
            ts_codes_str = ','.join(ts_codes)

            # 获取实时数据
            df = self._retry_request(
                lambda: self.pro.daily_basic(
                    ts_code=ts_codes_str,
                    trade_date='',  # 最新交易日
                    fields='ts_code,trade_date,close,turnover_rate,volume_ratio,pe,pb'
                ),
                "获取实时数据"
            )

            if df is None or df.empty:
                self.logger.warning("获取实时数据为空")
                return []

            result = []
            for _, row in df.iterrows():
                stock_code = self._convert_ts_code_to_standard(row['ts_code'])
                data = {
                    'stock_code': stock_code,
                    'trade_date': self._parse_date(row['trade_date']),
                    'close_price': float(row['close']) if pd.notna(row['close']) else 0.0,
                    'turnover_rate': float(row['turnover_rate']) if pd.notna(row['turnover_rate']) else 0.0,
                    'volume_ratio': float(row['volume_ratio']) if pd.notna(row['volume_ratio']) else 0.0,
                    'pe': float(row['pe']) if pd.notna(row['pe']) else 0.0,
                    'pb': float(row['pb']) if pd.notna(row['pb']) else 0.0
                }
                result.append(data)

            self.logger.info(f"成功获取 {len(result)} 只股票的实时数据")
            return result

        except Exception as e:
            self.logger.error(f"获取实时数据失败: {str(e)}")
            raise DataSourceError(f"获取实时数据失败: {str(e)}")

    def validate_stock_code(self, stock_code: str) -> bool:
        """验证股票代码是否有效"""
        try:
            # 基本格式验证
            if not stock_code or len(stock_code) != 6 or not stock_code.isdigit():
                return False

            # 通过获取股票基本信息验证
            ts_code = self._convert_standard_to_ts_code(stock_code)
            df = self.pro.stock_basic(ts_code=ts_code, fields='ts_code')
            return not df.empty

        except Exception as e:
            self.logger.error(f"验证股票代码 {stock_code} 失败: {str(e)}")
            return False

    def get_trading_calendar(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取交易日历"""
        try:
            cache_key = f"{start_date.date()}_{end_date.date()}"
            if cache_key in self._cached_trading_calendar:
                return self._cached_trading_calendar[cache_key]

            start_str = start_date.strftime('%Y%m%d')
            end_str = end_date.strftime('%Y%m%d')

            df = self._retry_request(
                lambda: self.pro.trade_cal(
                    exchange='SSE',
                    start_date=start_str,
                    end_date=end_str,
                    fields='cal_date,is_open'
                ),
                "获取交易日历"
            )

            if df is None or df.empty:
                raise DataSourceError("获取交易日历失败")

            # 筛选交易日
            trading_dates = []
            for _, row in df.iterrows():
                if row['is_open'] == 1:  # 1表示交易日
                    trading_dates.append(self._parse_date(row['cal_date']))

            # 缓存结果
            self._cached_trading_calendar[cache_key] = trading_dates
            return trading_dates

        except Exception as e:
            self.logger.error(f"获取交易日历失败: {str(e)}")
            raise DataSourceError(f"获取交易日历失败: {str(e)}")

    def _retry_request(self, request_func, operation_name: str):
        """重试机制执行请求"""
        for attempt in range(self.config.retry_times):
            try:
                # 添加随机延时，避免请求过于频繁
                if attempt > 0:
                    delay = random.uniform(*self.config.request_delay_range)
                    time.sleep(delay)

                result = request_func()
                return result

            except Exception as e:
                self.logger.warning(f"{operation_name} 第 {attempt + 1} 次尝试失败: {str(e)}")
                if attempt == self.config.retry_times - 1:
                    raise e
                time.sleep(self.config.retry_delay * (attempt + 1))

        return None

    def _convert_ts_code_to_standard(self, ts_code: str) -> str:
        """将tushare代码格式转换为标准格式"""
        # tushare格式: 000001.SZ -> 标准格式: 000001
        if '.' in ts_code:
            return ts_code.split('.')[0]
        return ts_code

    def _convert_standard_to_ts_code(self, stock_code: str) -> str:
        """将标准格式转换为tushare代码格式"""
        # 标准格式: 000001 -> tushare格式: 000001.SZ
        if len(stock_code) == 6 and stock_code.isdigit():
            if stock_code.startswith(('000', '001', '002', '003')):
                return f"{stock_code}.SZ"  # 深交所
            elif stock_code.startswith(('600', '601', '603', '605')):
                return f"{stock_code}.SH"  # 上交所
            elif stock_code.startswith('688'):
                return f"{stock_code}.SH"  # 科创板
            elif stock_code.startswith('300'):
                return f"{stock_code}.SZ"  # 创业板
        return stock_code

    def _determine_market_from_ts_code(self, ts_code: str) -> str:
        """根据tushare代码确定市场"""
        if ts_code.endswith('.SH'):
            code = ts_code.split('.')[0]
            if code.startswith('688'):
                return '科创板'
            else:
                return '主板'
        elif ts_code.endswith('.SZ'):
            code = ts_code.split('.')[0]
            if code.startswith('300'):
                return '创业板'
            elif code.startswith('000'):
                return '主板'
            elif code.startswith('002'):
                return '中小板'
        return '未知'

    def _parse_date(self, date_str: str) -> datetime:
        """解析日期字符串"""
        if isinstance(date_str, str) and len(date_str) == 8:
            return datetime.strptime(date_str, '%Y%m%d')
        return datetime.now()

    def _convert_daily_data(self, df: pd.DataFrame, stock_code: str) -> List[Dict]:
        """转换日K线数据格式"""
        result = []
        for _, row in df.iterrows():
            data = {
                'stock_code': stock_code,
                'trade_date': self._parse_date(row['trade_date']),
                'open_price': float(row['open']) if pd.notna(row['open']) else 0.0,
                'close_price': float(row['close']) if pd.notna(row['close']) else 0.0,
                'high_price': float(row['high']) if pd.notna(row['high']) else 0.0,
                'low_price': float(row['low']) if pd.notna(row['low']) else 0.0,
                'volume': int(row['vol']) if pd.notna(row['vol']) else 0,
                'amount': float(row['amount']) if pd.notna(row['amount']) else 0.0,
                'turnover_rate': 0.0  # tushare日线数据中没有换手率，需要单独获取
            }
            result.append(data)

        # 按日期排序
        result.sort(key=lambda x: x['trade_date'])
        return result

    def get_batch_daily_data(self, stock_codes: List[str],
                           start_date: datetime,
                           end_date: datetime) -> Dict[str, List[Dict]]:
        """批量获取多只股票的日K线数据"""
        result = {}
        total_count = len(stock_codes)

        self.logger.info(f"开始批量获取 {total_count} 只股票的数据")

        for i, stock_code in enumerate(stock_codes, 1):
            try:
                self.logger.info(f"获取股票 {stock_code} 数据 ({i}/{total_count})")

                daily_data = self.get_daily_data(stock_code, start_date, end_date)
                result[stock_code] = daily_data

                # 添加随机延时，避免请求过于频繁
                if i < total_count:  # 最后一个不需要延时
                    delay = random.uniform(*self.config.request_delay_range)
                    time.sleep(delay)

            except Exception as e:
                self.logger.error(f"获取股票 {stock_code} 数据失败: {str(e)}")
                result[stock_code] = []
                continue

        success_count = sum(1 for data in result.values() if data)
        self.logger.info(f"批量获取完成，成功: {success_count}/{total_count}")

        return result
