"""
优化的数据获取服务
专注于T-1交易日数据获取，提供高效的批量处理和智能缓存
"""
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional
from concurrent.futures import ThreadPoolExecutor, as_completed
import time

from ..sources.akshare_source import AkshareDataSource
from ...core.interfaces.data_access import IDataAccess
from ...core.exceptions.custom_exceptions import DataSourceError


class OptimizedDataService:
    """优化的数据获取服务"""

    def __init__(self, data_source: AkshareDataSource, data_access: IDataAccess):
        self.data_source = data_source
        self.data_access = data_access
        self.logger = logging.getLogger(__name__)
        self.max_workers = 3  # 并发线程数，避免过多并发请求
        self.batch_size = 20  # 每批处理的股票数量

    def update_incremental_data_optimized(self) -> bool:
        """
        优化的增量数据更新
        只获取T-1交易日的数据，不需要实时数据
        """
        try:
            self.logger.info("开始优化的增量数据更新...")

            # 获取需要更新的股票列表
            stocks_need_update = self.data_access.get_stocks_need_update()

            if not stocks_need_update:
                self.logger.info("所有股票数据都是最新的，无需更新")
                return True

            self.logger.info(f"需要更新数据的股票数量: {len(stocks_need_update)}")

            # 获取最新交易日
            latest_trading_date = self._get_latest_trading_date()
            if not latest_trading_date:
                self.logger.error("无法获取最新交易日")
                return False

            # 分批处理股票
            success_count = 0
            total_count = len(stocks_need_update)

            for i in range(0, total_count, self.batch_size):
                batch_stocks = stocks_need_update[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (total_count + self.batch_size - 1) // self.batch_size

                self.logger.info(f"处理第 {batch_num}/{total_batches} 批，股票数量: {len(batch_stocks)}")

                # 批量获取数据
                batch_success = self._process_batch_incremental(
                    batch_stocks, latest_trading_date
                )
                success_count += batch_success

                # 批次间延时
                if i + self.batch_size < total_count:
                    time.sleep(1)

            self.logger.info(f"增量数据更新完成，成功: {success_count}/{total_count}")
            return success_count == total_count

        except Exception as e:
            self.logger.error(f"优化的增量数据更新失败: {str(e)}")
            return False

    def update_historical_data_optimized(self, 
                                       start_date: datetime, 
                                       end_date: datetime,
                                       stock_codes: Optional[List[str]] = None) -> bool:
        """
        优化的历史数据补充
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            stock_codes: 指定股票代码列表，None表示所有股票
        """
        try:
            self.logger.info(f"开始优化的历史数据补充: {start_date.date()} 到 {end_date.date()}")

            # 获取股票列表
            if stock_codes is None:
                stock_codes = self.data_access.get_all_stock_codes()

            if not stock_codes:
                self.logger.warning("没有找到需要处理的股票")
                return False

            self.logger.info(f"需要处理的股票数量: {len(stock_codes)}")

            # 分批处理
            success_count = 0
            total_count = len(stock_codes)

            for i in range(0, total_count, self.batch_size):
                batch_stocks = stock_codes[i:i + self.batch_size]
                batch_num = i // self.batch_size + 1
                total_batches = (total_count + self.batch_size - 1) // self.batch_size

                self.logger.info(f"处理第 {batch_num}/{total_batches} 批，股票数量: {len(batch_stocks)}")

                # 批量获取数据
                batch_success = self._process_batch_historical(
                    batch_stocks, start_date, end_date
                )
                success_count += batch_success

                # 批次间延时
                if i + self.batch_size < total_count:
                    time.sleep(2)  # 历史数据获取间隔稍长

            self.logger.info(f"历史数据补充完成，成功: {success_count}/{total_count}")
            return success_count == total_count

        except Exception as e:
            self.logger.error(f"优化的历史数据补充失败: {str(e)}")
            return False

    def _process_batch_incremental(self, stock_codes: List[str], target_date: datetime) -> int:
        """处理增量数据批次"""
        success_count = 0

        for stock_code in stock_codes:
            try:
                # 获取股票最新数据日期
                latest_date = self.data_access.get_latest_trade_date(stock_code)
                
                if latest_date and latest_date >= target_date:
                    self.logger.debug(f"股票 {stock_code} 数据已是最新")
                    success_count += 1
                    continue

                # 确定需要获取的日期范围
                start_date = latest_date + timedelta(days=1) if latest_date else target_date
                
                # 获取数据
                daily_data = self.data_source.get_daily_data(stock_code, start_date, target_date)

                if daily_data:
                    # 保存数据
                    if self.data_access.save_daily_data(daily_data):
                        success_count += 1
                        self.logger.info(f"股票 {stock_code} 增量数据更新成功，新增 {len(daily_data)} 条记录")
                    else:
                        self.logger.error(f"股票 {stock_code} 数据保存失败")
                else:
                    self.logger.info(f"股票 {stock_code} 在指定时间段内无新数据")
                    success_count += 1

            except Exception as e:
                self.logger.error(f"处理股票 {stock_code} 增量数据失败: {str(e)}")
                continue

        return success_count

    def _process_batch_historical(self, stock_codes: List[str], 
                                start_date: datetime, end_date: datetime) -> int:
        """处理历史数据批次"""
        success_count = 0

        for stock_code in stock_codes:
            try:
                # 检查缺失的日期
                missing_dates = self.data_access.get_missing_dates(stock_code, start_date, end_date)

                if not missing_dates:
                    self.logger.debug(f"股票 {stock_code} 在指定时间段内数据完整")
                    success_count += 1
                    continue

                self.logger.info(f"股票 {stock_code} 需要补充 {len(missing_dates)} 个交易日的数据")

                # 获取整个时间段的数据（akshare一次性获取更高效）
                daily_data = self.data_source.get_daily_data(stock_code, start_date, end_date)

                if daily_data:
                    # 保存数据
                    if self.data_access.save_daily_data(daily_data):
                        success_count += 1
                        self.logger.info(f"股票 {stock_code} 历史数据补充成功，共 {len(daily_data)} 条记录")
                    else:
                        self.logger.error(f"股票 {stock_code} 数据保存失败")
                else:
                    self.logger.warning(f"股票 {stock_code} 在指定时间段内无数据")
                    success_count += 1  # 无数据也算成功

            except Exception as e:
                self.logger.error(f"处理股票 {stock_code} 历史数据失败: {str(e)}")
                continue

        return success_count

    def _get_latest_trading_date(self) -> Optional[datetime]:
        """获取最新交易日"""
        try:
            # 获取最近7天的交易日历，找到最新的交易日
            end_date = datetime.now()
            start_date = end_date - timedelta(days=7)
            
            trading_dates = self.data_source.get_trading_calendar(start_date, end_date)
            
            if trading_dates:
                # 返回最新的交易日
                latest_date = max(trading_dates)
                self.logger.info(f"最新交易日: {latest_date.date()}")
                return latest_date
            else:
                self.logger.warning("无法获取交易日历")
                return None

        except Exception as e:
            self.logger.error(f"获取最新交易日失败: {str(e)}")
            return None

    def get_data_statistics(self) -> Dict:
        """获取数据统计信息"""
        try:
            stats = {
                'total_stocks': len(self.data_access.get_all_stock_codes()),
                'stocks_need_update': len(self.data_access.get_stocks_need_update()),
                'latest_trading_date': self._get_latest_trading_date(),
                'cache_status': {
                    'stock_list_cached': self.data_source._cached_stock_list is not None,
                    'trading_calendar_cached': len(self.data_source._cached_trading_calendar)
                }
            }
            return stats

        except Exception as e:
            self.logger.error(f"获取数据统计信息失败: {str(e)}")
            return {}

    def clear_all_cache(self):
        """清除所有缓存"""
        self.data_source.clear_cache()
        self.logger.info("所有缓存已清除")
