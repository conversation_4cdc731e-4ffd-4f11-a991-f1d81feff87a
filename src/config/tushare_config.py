"""
Tushare数据源配置
"""
import os
from typing import Optional


class TushareConfig:
    """Tushare配置类"""
    
    def __init__(self):
        # 从环境变量获取token，如果没有则使用默认值
        self.token = os.getenv('TUSHARE_TOKEN', '')
        
        # API配置
        self.api_timeout = 30  # API超时时间（秒）
        self.retry_times = 3   # 重试次数
        self.retry_delay = 1   # 重试延时（秒）
        
        # 请求频率控制
        self.request_delay_range = (0.1, 0.3)  # 随机延时范围（秒）
        self.max_requests_per_minute = 200     # 每分钟最大请求数
        
        # 数据获取配置
        self.batch_size = 50   # 批量处理大小
        self.max_workers = 3   # 并发线程数
        
    def get_token(self) -> str:
        """获取tushare token"""
        if not self.token:
            raise ValueError(
                "Tushare token未配置。请设置环境变量TUSHARE_TOKEN或在配置文件中设置token"
            )
        return self.token
    
    def set_token(self, token: str) -> None:
        """设置tushare token"""
        self.token = token
        
    def is_token_configured(self) -> bool:
        """检查token是否已配置"""
        return bool(self.token)


# 全局配置实例
tushare_config = TushareConfig()
