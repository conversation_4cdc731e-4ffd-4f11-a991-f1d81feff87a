#!/usr/bin/env python3
"""
A股智能选股系统主程序
v0.1版本：基础框架和数据获取功能
"""
import sys
import os
import argparse
import time
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.data.sources.tushare_source import TushareDataSource
from src.data.access.data_access_factory import DataAccessFactory
from src.data.services.optimized_data_service import OptimizedDataService
from src.core.interfaces.data_access import IDataAccess
from src.utils.logger import setup_logger


def init_system():
    """初始化系统"""
    # 设置日志
    logger = setup_logger(
        name="stock_selection",
        log_level="INFO",
        log_file="logs/app.log"
    )

    # 确保必要目录存在
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data", exist_ok=True)

    logger.info("系统初始化完成")
    return logger


def update_stock_list(data_source: TushareDataSource,
                     data_access: IDataAccess,
                     logger) -> bool:
    """更新股票列表"""
    try:
        logger.info("开始更新股票列表...")

        # 获取股票列表
        stock_list = data_source.get_stock_list()

        # 保存到数据库
        success = data_access.save_stock_info_batch(stock_list)

        if success:
            logger.info(f"股票列表更新成功，共 {len(stock_list)} 只股票")
            return True
        else:
            logger.error("股票列表保存失败")
            return False

    except Exception as e:
        logger.error(f"更新股票列表失败: {str(e)}")
        return False


def update_daily_data(data_source: TushareDataSource,
                     data_access: IDataAccess,
                     logger,
                     days: int = 30,
                     limit: int = None) -> bool:
    """更新日交易数据"""
    try:
        logger.info(f"开始更新最近 {days} 天的交易数据...")

        # 获取所有股票代码
        stock_codes = data_access.get_all_stock_codes()

        if not stock_codes:
            logger.warning("数据库中没有股票信息，请先更新股票列表")
            return False

        # 限制处理数量（用于测试）
        if limit:
            stock_codes = stock_codes[:limit]
            logger.info(f"限制处理股票数量: {limit}")

        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        success_count = 0
        total_count = len(stock_codes)

        for i, stock_code in enumerate(stock_codes, 1):
            try:
                logger.info(f"处理股票 {stock_code} ({i}/{total_count})")

                # 获取日K线数据
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)

                if daily_data:
                    # 保存数据
                    data_access.save_daily_data(daily_data)
                    success_count += 1
                    logger.info(f"  成功保存 {len(daily_data)} 条记录")
                else:
                    logger.warning(f"  股票 {stock_code} 无数据")

            except Exception as e:
                logger.error(f"  处理股票 {stock_code} 失败: {str(e)}")
                continue

        logger.info(f"交易数据更新完成，成功处理 {success_count}/{total_count} 只股票")
        return success_count > 0

    except Exception as e:
        logger.error(f"更新交易数据失败: {str(e)}")
        return False


def update_incremental_data(data_source: TushareDataSource,
                           data_access: IDataAccess,
                           logger) -> bool:
    """增量更新数据"""
    try:
        logger.info("开始增量数据更新...")

        # 获取需要更新的股票列表
        stocks_need_update = data_access.get_stocks_need_update()

        if not stocks_need_update:
            logger.info("所有股票数据都是最新的，无需更新")
            return True

        logger.info(f"需要更新数据的股票数量: {len(stocks_need_update)}")

        success_count = 0
        total_count = len(stocks_need_update)

        for i, stock_code in enumerate(stocks_need_update, 1):
            try:
                logger.info(f"处理股票 {stock_code} ({i}/{total_count})")

                # 获取该股票的最新交易日期
                latest_date = data_access.get_latest_trade_date(stock_code)

                # 确定更新的开始日期
                if latest_date:
                    start_date = latest_date + timedelta(days=1)
                else:
                    # 如果没有历史数据，获取最近30天的数据
                    start_date = datetime.now() - timedelta(days=30)

                end_date = datetime.now()

                # 如果开始日期已经是今天或未来，跳过
                if start_date.date() >= end_date.date():
                    logger.info(f"股票 {stock_code} 数据已是最新，跳过")
                    success_count += 1
                    continue

                # 获取数据
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)

                if daily_data:
                    # 保存数据
                    if data_access.save_daily_data(daily_data):
                        success_count += 1
                        logger.info(f"股票 {stock_code} 增量数据更新成功，新增 {len(daily_data)} 条记录")
                    else:
                        logger.error(f"股票 {stock_code} 数据保存失败")
                else:
                    logger.info(f"股票 {stock_code} 在指定时间段内无新数据")
                    success_count += 1

                # 避免请求过于频繁
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"处理股票 {stock_code} 失败: {str(e)}")
                continue

        logger.info(f"增量数据更新完成，成功: {success_count}/{total_count}")
        return success_count == total_count

    except Exception as e:
        logger.error(f"增量数据更新失败: {str(e)}")
        return False


def update_historical_data(data_source: TushareDataSource,
                          data_access: IDataAccess,
                          logger,
                          start_date: datetime,
                          end_date: datetime,
                          stock_codes: List[str] = None) -> bool:
    """历史数据补充"""
    try:
        logger.info(f"开始历史数据补充：{start_date.date()} 到 {end_date.date()}")

        # 如果没有指定股票代码，获取所有股票
        if stock_codes is None:
            stock_codes = data_access.get_all_stock_codes()

        if not stock_codes:
            logger.warning("没有找到股票代码")
            return False

        logger.info(f"需要处理的股票数量: {len(stock_codes)}")

        success_count = 0
        total_count = len(stock_codes)

        for i, stock_code in enumerate(stock_codes, 1):
            try:
                logger.info(f"处理股票 {stock_code} ({i}/{total_count})")

                # 获取缺失的日期
                missing_dates = data_access.get_missing_dates(stock_code, start_date, end_date)

                if not missing_dates:
                    logger.info(f"股票 {stock_code} 在指定时间段内数据完整，跳过")
                    success_count += 1
                    continue

                logger.info(f"股票 {stock_code} 需要补充 {len(missing_dates)} 个交易日的数据")

                # 获取整个时间段的数据（akshare一次性获取更高效）
                daily_data = data_source.get_daily_data(stock_code, start_date, end_date)

                if daily_data:
                    # 保存数据
                    if data_access.save_daily_data(daily_data):
                        success_count += 1
                        logger.info(f"股票 {stock_code} 历史数据补充成功，共 {len(daily_data)} 条记录")
                    else:
                        logger.error(f"股票 {stock_code} 数据保存失败")
                else:
                    logger.warning(f"股票 {stock_code} 在指定时间段内无数据")
                    success_count += 1  # 无数据也算成功

                # 避免请求过于频繁
                time.sleep(0.1)

            except Exception as e:
                logger.error(f"处理股票 {stock_code} 失败: {str(e)}")
                continue

        logger.info(f"历史数据补充完成，成功: {success_count}/{total_count}")
        return success_count == total_count

    except Exception as e:
        logger.error(f"历史数据补充失败: {str(e)}")
        return False


def show_statistics(data_access: IDataAccess, logger):
    """显示统计信息"""
    try:
        logger.info("系统统计信息:")

        # 股票数量
        stock_codes = data_access.get_all_stock_codes()
        logger.info(f"  股票总数: {len(stock_codes)}")

        # 随机检查几只股票的数据情况
        if stock_codes:
            sample_codes = stock_codes[:5]
            logger.info("  样本股票数据情况:")

            for stock_code in sample_codes:
                latest_date = data_access.get_latest_trade_date(stock_code)
                stock_info = data_access.get_stock_info(stock_code)

                if stock_info and latest_date:
                    logger.info(f"    {stock_code} - {stock_info['stock_name']}: "
                              f"最新数据 {latest_date.strftime('%Y-%m-%d')}")
                else:
                    logger.info(f"    {stock_code}: 无交易数据")

    except Exception as e:
        logger.error(f"获取统计信息失败: {str(e)}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="A股智能选股系统 v0.1")
    parser.add_argument("--update-stocks", action="store_true",
                       help="更新股票列表")
    parser.add_argument("--update-data", action="store_true",
                       help="更新交易数据")
    parser.add_argument("--incremental", action="store_true",
                       help="增量更新数据（智能检测需要更新的股票）")
    parser.add_argument("--incremental-optimized", action="store_true",
                       help="优化的增量更新数据（专注T-1交易日数据）")
    parser.add_argument("--historical", action="store_true",
                       help="历史数据补充")
    parser.add_argument("--historical-optimized", action="store_true",
                       help="优化的历史数据补充（批量处理，智能缓存）")
    parser.add_argument("--days", type=int, default=30,
                       help="更新最近N天的数据（默认30天）")
    parser.add_argument("--start-date", type=str,
                       help="开始日期（格式：YYYY-MM-DD）")
    parser.add_argument("--end-date", type=str,
                       help="结束日期（格式：YYYY-MM-DD）")
    parser.add_argument("--stocks", type=str,
                       help="指定股票代码，多个用逗号分隔（如：000001,000002）")
    parser.add_argument("--limit", type=int,
                       help="限制处理的股票数量（用于测试）")
    parser.add_argument("--stats", action="store_true",
                       help="显示统计信息")

    args = parser.parse_args()

    # 初始化系统
    logger = init_system()
    logger.info("A股智能选股系统 v0.1 启动")

    try:
        # 创建数据源和数据访问实例
        data_source = TushareDataSource()
        data_access = DataAccessFactory.create_data_access()  # 默认使用MySQL

        # 创建优化数据服务
        optimized_service = OptimizedDataService(data_source, data_access)

        logger.info(f"数据源: {data_source.get_data_source_name()}")
        logger.info("优化数据服务已初始化")

        # 根据参数执行相应操作
        if args.update_stocks:
            success = update_stock_list(data_source, data_access, logger)
            if not success:
                logger.error("股票列表更新失败")
                return False

        if args.update_data:
            success = update_daily_data(data_source, data_access, logger,
                                      args.days, args.limit)
            if not success:
                logger.error("交易数据更新失败")
                return False

        if args.incremental:
            success = update_incremental_data(data_source, data_access, logger)
            if not success:
                logger.error("增量数据更新失败")
                return False

        if args.incremental_optimized:
            success = optimized_service.update_incremental_data_optimized()
            if not success:
                logger.error("优化的增量数据更新失败")
                return False

        if args.historical:
            # 解析日期参数
            try:
                if args.start_date:
                    start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
                else:
                    start_date = datetime.now() - timedelta(days=90)  # 默认最近90天

                if args.end_date:
                    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
                else:
                    end_date = datetime.now()

                # 解析股票代码参数
                stock_codes = None
                if args.stocks:
                    stock_codes = [code.strip() for code in args.stocks.split(',')]

                success = update_historical_data(data_source, data_access, logger,
                                               start_date, end_date, stock_codes)
                if not success:
                    logger.error("历史数据补充失败")
                    return False

            except ValueError as e:
                logger.error(f"日期格式错误: {str(e)}")
                return False

        if args.historical_optimized:
            # 解析日期参数
            try:
                if args.start_date:
                    start_date = datetime.strptime(args.start_date, '%Y-%m-%d')
                else:
                    start_date = datetime.now() - timedelta(days=90)  # 默认最近90天

                if args.end_date:
                    end_date = datetime.strptime(args.end_date, '%Y-%m-%d')
                else:
                    end_date = datetime.now()

                # 解析股票代码参数
                stock_codes = None
                if args.stocks:
                    stock_codes = [code.strip() for code in args.stocks.split(',')]

                success = optimized_service.update_historical_data_optimized(
                    start_date, end_date, stock_codes)
                if not success:
                    logger.error("优化的历史数据补充失败")
                    return False

            except ValueError as e:
                logger.error(f"日期格式错误: {str(e)}")
                return False

        # 如果没有指定任何操作，显示统计信息
        if args.stats or (not args.update_stocks and not args.update_data and
                         not args.incremental and not args.historical and
                         not args.incremental_optimized and not args.historical_optimized):
            show_statistics(data_access, logger)

            # 显示优化服务的统计信息
            stats = optimized_service.get_data_statistics()
            if stats:
                logger.info("优化服务统计信息:")
                logger.info(f"  总股票数: {stats.get('total_stocks', 0)}")
                logger.info(f"  需要更新的股票数: {stats.get('stocks_need_update', 0)}")
                if stats.get('latest_trading_date'):
                    logger.info(f"  最新交易日: {stats['latest_trading_date'].date()}")
                cache_status = stats.get('cache_status', {})
                logger.info(f"  缓存状态: 股票列表={cache_status.get('stock_list_cached', False)}, "
                          f"交易日历={cache_status.get('trading_calendar_cached', 0)}年")

        logger.info("程序执行完成")
        return True

    except Exception as e:
        logger.error(f"程序执行失败: {str(e)}")
        return False

    finally:
        # 清理资源
        if 'data_access' in locals():
            data_access.close_connection()


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
