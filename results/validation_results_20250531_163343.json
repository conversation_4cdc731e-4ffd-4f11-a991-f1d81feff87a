{"timestamp": "2025-05-31T16:33:43.964063", "total_stocks": 2, "successful_validations": 2, "results": [{"stock_code": "601111", "stock_name": "601111", "strategy_name": "technical_reversal", "start_date": "2024-04-01T00:00:00", "end_date": "2024-04-30T00:00:00", "total_signals": 3, "chart_path": "charts/batch_601111_technical_reversal_20240401.png", "error_message": null, "signal_points": [{"date": "2024-04-12T00:00:00", "price": 7.09, "score": 103, "signal_type": "buy", "indicators": {"rsi": 34.58646616541351, "volume_ratio": 1.691868970597991, "price_position": 0, "bollinger_position": 0}, "reason": "技术反转信号: 突破布林带下轨, 成交量放大1.69倍, RSI(34.6)<35.0超卖"}, {"date": "2024-04-15T00:00:00", "price": 7.2, "score": 84.1, "signal_type": "buy", "indicators": {"rsi": 43.84615384615383, "volume_ratio": 1.0471563954627177, "price_position": 0, "bollinger_position": 0}, "reason": "技术反转信号: 突破布林带下轨, 成交量放大1.05倍, RSI(43.8)偏高"}, {"date": "2024-04-19T00:00:00", "price": 7.09, "score": 84.35, "signal_type": "buy", "indicators": {"rsi": 43.47826086956522, "volume_ratio": 1.4974519702505475, "price_position": 0, "bollinger_position": 0}, "reason": "技术反转信号: 突破布林带下轨, 成交量放大1.50倍, RSI(43.5)偏高"}]}, {"stock_code": "600036", "stock_name": "600036", "strategy_name": "technical_reversal", "start_date": "2024-04-01T00:00:00", "end_date": "2024-04-30T00:00:00", "total_signals": 0, "chart_path": "charts/batch_600036_technical_reversal_20240401.png", "error_message": null, "signal_points": []}]}